import 'package:pip_view/pip_view.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:flowkar/core/utils/exports.dart';

class PipService {
  static final PipService _instance = PipService._internal();
  factory PipService() => _instance;
  PipService._internal();

  bool _isPipActive = false;
  RTCVideoRenderer? _currentRenderer;
  String? _currentStreamId;
  String? _currentUserName;
  String? _currentUserImage;
  VoidCallback? _onPipTap;
  VoidCallback? _onPipClose;
  BuildContext? _currentContext;

  bool get isPipActive => _isPipActive;

  /// Start PIP mode for live stream
  Future<void> startPipMode({
    required BuildContext context,
    required RTCVideoRenderer renderer,
    required String streamId,
    required String userName,
    required String userImage,
    VoidCallback? onTap,
    VoidCallback? onClose,
  }) async {
    try {
      if (_isPipActive) {
        await stopPipMode(context);
      }

      _currentRenderer = renderer;
      _currentStreamId = streamId;
      _currentUserName = userName;
      _currentUserImage = userImage;
      _onPipTap = onTap;
      _onPipClose = onClose;
      _currentContext = context;

      PIPView.of(context)?.presentBelow(
        _buildPipWidget(),
      );

      _isPipActive = true;
      Logger.lOG('PIP mode started for stream: $streamId');
    } catch (e) {
      Logger.lOG('Error starting PIP mode: $e');
    }
  }

  /// Stop PIP mode
  Future<void> stopPipMode(BuildContext context) async {
    try {
      if (_isPipActive) {
        PIPView.of(context)?.stopFloating();
        _isPipActive = false;
        _currentRenderer = null;
        _currentStreamId = null;
        _currentUserName = null;
        _currentUserImage = null;
        _onPipTap = null;
        _onPipClose = null;
        _currentContext = null;
        Logger.lOG('PIP mode stopped');
      }
    } catch (e) {
      Logger.lOG('Error stopping PIP mode: $e');
    }
  }

  /// Update PIP content with new renderer
  void updatePipContent({
    RTCVideoRenderer? renderer,
    String? userName,
    String? userImage,
  }) {
    if (_isPipActive) {
      if (renderer != null) _currentRenderer = renderer;
      if (userName != null) _currentUserName = userName;
      if (userImage != null) _currentUserImage = userImage;
    }
  }

  /// Build the PIP widget
  Widget _buildPipWidget() {
    return Container(
      width: 120.w,
      height: 160.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 2.w,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10.r),
        child: Stack(
          children: [
            // Video content
            if (_currentRenderer != null)
              Positioned.fill(
                child: RTCVideoView(
                  _currentRenderer!,
                  objectFit: RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
                  mirror: false,
                ),
              ),

            // Fallback when no video
            if (_currentRenderer == null)
              Positioned.fill(
                child: Container(
                  color: Colors.black87,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_currentUserImage != null)
                          CircleAvatar(
                            radius: 20.r,
                            backgroundImage: NetworkImage(
                              "${APIConfig.mainbaseURL}$_currentUserImage",
                            ),
                          ),
                        SizedBox(height: 8.h),
                        Text(
                          _currentUserName ?? 'Live Stream',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // Live indicator
            Positioned(
              top: 8.h,
              left: 8.w,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  'LIVE',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 8.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

            // Close button
            Positioned(
              top: 4.h,
              right: 4.w,
              child: GestureDetector(
                onTap: () {
                  if (_currentContext != null) {
                    stopPipMode(_currentContext!);
                  }
                  _onPipClose?.call();
                },
                child: Container(
                  width: 20.w,
                  height: 20.h,
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 12.sp,
                  ),
                ),
              ),
            ),

            // Tap detector for the entire PIP
            Positioned.fill(
              child: GestureDetector(
                onTap: () {
                  _onPipTap?.call();
                },
                child: Container(
                  color: Colors.transparent,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
